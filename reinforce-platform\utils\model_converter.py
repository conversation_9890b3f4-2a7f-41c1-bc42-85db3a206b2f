#!/usr/bin/env python3
"""
模型推理工具

此模块提供了模型推理的功能，支持使用YOLOv8模型进行图像推理。
"""

import os
import logging
import paramiko

# 导入环境变量函数
try:
    from environ import Env
    env = Env()
except ImportError:
    # 如果没有安装django-environ，使用简单的环境变量读取
    def env(key, default=None):
        return os.environ.get(key, default)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelConverter:
    """
    模型推理工具类

    提供模型推理功能，支持远程执行和结果获取。
    """
    
    def __init__(self, server_info=None):
        """
        初始化模型推理器

        Args:
            server_info: 服务器连接信息，包含以下字段：
                - ip: 服务器IP地址
                - port: SSH端口
                - username: 用户名，默认为'root'
                - password: 密码
        """
        self.server_info = server_info
        self.ssh_client = None
        self.remote_dir = "/workspace"
        
        # 如果提供了服务器信息，则自动连接
        if server_info:
            self.connect()
    
    def connect(self, server_info=None):
        """
        连接到远程服务器
        
        Args:
            server_info: 服务器连接信息，如果为None则使用初始化时提供的信息
            
        Returns:
            bool: 连接成功返回True，否则返回False
        """
        if server_info:
            self.server_info = server_info
            
        if not self.server_info:
            logger.error("未提供服务器连接信息")
            return False
            
        try:
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接远程服务器
            username = self.server_info.get('username', 'root')
            logger.info(f"正在连接服务器: {self.server_info['ip']}:{self.server_info['port']}")
            self.ssh_client.connect(
                hostname=self.server_info['ip'],
                port=int(self.server_info['port']),
                username=username,
                password=self.server_info['password'],
                timeout=30
            )
            logger.info("SSH连接成功")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            self.ssh_client = None
            return False
    
    def disconnect(self):
        """
        断开与远程服务器的连接
        """
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
            logger.info("SSH连接已关闭")
    
    def _get_remote_script_path(self, script_name):
        """
        获取远程脚本路径
        
        Args:
            script_name: 脚本名称
            
        Returns:
            str: 远程脚本路径
        """
        return f"{self.remote_dir}/{script_name}"
    
    def upload_script(self, local_path, remote_name=None):
        """
        上传脚本到远程服务器
        
        Args:
            local_path: 本地脚本路径
            remote_name: 远程脚本名称，如果为None则使用本地文件名
            
        Returns:
            bool: 上传成功返回True，否则返回False
        """
        if not self.ssh_client:
            logger.error("SSH未连接，无法上传文件")
            return False
            
        try:
            # 确保远程工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()
            
            # 确定远程文件名
            if not remote_name:
                remote_name = os.path.basename(local_path)
            
            # 上传文件
            remote_path = f"{self.remote_dir}/{remote_name}"
            logger.info(f"上传脚本: {local_path} -> {remote_path}")
            sftp.put(local_path, remote_path)
            
            # 关闭SFTP连接
            sftp.close()
            
            logger.info(f"脚本上传成功: {remote_name}")
            return True
        except Exception as e:
            logger.error(f"上传脚本失败: {e}")
            return False
    

    
    def run_inference(self, model_path, input_source, confidence_threshold=0.5, output_path=None):
        """
        运行模型推理（单张图片）

        Args:
            model_path: 模型文件路径
            input_source: 输入源 (图片路径或base64数据)
            confidence_threshold: 置信度阈值
            output_path: 输出路径 (可选)

        Returns:
            dict: 推理结果，包含图片数据
        """
        try:
            if not self.ssh_client:
                return {"success": False, "error": "SSH未连接"}

            # 获取远程脚本路径
            remote_script_path = self._get_remote_script_path("model_info_extractor.py")

            # 获取ULTRALYTICS_DIR用于--mount_path参数
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')
            
            # 获取挂载路径
            mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 设置环境变量命令
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

            # 处理输入源 - 如果是base64数据，先上传到远程服务器
            processed_input_source = input_source
            temp_file_created = False

            # 检查是否为base64数据（长度超过200字符且不是文件路径）
            if len(input_source) > 200:
                try:
                    import base64
                    import tempfile
                    import time

                    logger.info("检测到base64数据，正在上传到远程服务器...")

                    # 生成唯一的临时文件名
                    timestamp = int(time.time())
                    temp_filename = f"temp_input_{timestamp}.jpg"
                    remote_temp_path = f"{self.remote_dir}/{temp_filename}"
                    print(remote_temp_path)

                    # 处理base64数据
                    if input_source.startswith('data:image/'):
                        # 移除data:image/...;base64,前缀
                        _, encoded_data = input_source.split(',', 1)
                    else:
                        encoded_data = input_source

                    # 解码base64数据
                    image_data = base64.b64decode(encoded_data)
                    # 创建本地临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as local_temp:
                        local_temp.write(image_data)
                        local_temp_path = local_temp.name

                    # 上传到远程服务器
                    sftp = self.ssh_client.open_sftp()
                    sftp.put(local_temp_path, remote_temp_path)
                    sftp.close()

                    # 删除本地临时文件
                    os.unlink(local_temp_path)

                    # 使用远程临时文件路径
                    processed_input_source = remote_temp_path
                    temp_file_created = True

                    logger.info(f"Base64数据已上传到: {remote_temp_path}")

                except Exception as e:
                    logger.error(f"处理base64数据失败: {e}")
                    # 如果处理失败，仍然尝试直接使用原始数据
                    processed_input_source = input_source

            # 构建推理命令
            infer_cmd = f"""
cd {self.remote_dir} && \
python {remote_script_path} infer --model {model_path} --image {processed_input_source} --conf {confidence_threshold} --ultralytics_mount_path {ultralytics_mount_path} --mount_path {mount_path}
"""

            if output_path:
                infer_cmd += f" --save_path {output_path}"

            # 组合完整命令，添加环境变量设置
            full_cmd = f"bash -c '{set_env_cmd} && {infer_cmd}'"
            
            # 显示处理后的输入源信息（避免打印长base64字符串）
            display_input = processed_input_source if len(processed_input_source) < 100 else f"{processed_input_source[:50]}...(truncated)"
            logger.info(f"🔍 开始模型推理: {model_path} -> {display_input}")

            # 执行推理命令
            _, stdout, stderr = self.ssh_client.exec_command(full_cmd)
            output = stdout.read().decode()
            error = stderr.read().decode()
            print(error)
            
            if error and "错误" in error:
                logger.error(f"❌ 模型推理失败: {error}")
                return {
                    "success": False,
                    "error": error,
                    "inference_log": output
                }
            
            # 从输出中提取结果信息
            result_img_path = None
            detections = []
            detection_count = 0

            logger.info(f"推理输出内容:\n{output}")

            for line in output.split('\n'):
                line = line.strip()
                if "推理完成，结果保存在" in line:
                    result_img_path = line.split(": ")[-1].strip()
                    logger.info(f"从输出中解析到结果路径: {result_img_path}")
                elif "INFERENCE_RESULT_PATH:" in line:
                    # 新的特殊标记格式
                    result_img_path = line.split("INFERENCE_RESULT_PATH:")[-1].strip()
                    logger.info(f"从特殊标记解析到结果路径: {result_img_path}")
                elif "检测到" in line and "个目标" in line:
                    try:
                        detection_count = int(line.split(" ")[1])
                        logger.info(f"检测到目标数量: {detection_count}")
                    except:
                        pass
                elif "目标" in line and "类别=" in line:
                    try:
                        parts = line.split(": ")[1].split(", ")
                        cls_name = parts[0].split("=")[1]
                        confidence = float(parts[1].split("=")[1])
                        coords = eval(parts[2].split("=")[1])

                        detections.append({
                            "class": cls_name,
                            "confidence": confidence,
                            "bbox": coords
                        })
                    except Exception as e:
                        logger.warning(f"解析检测结果行失败: {line}, 错误: {e}")

            # 如果指定了输出路径，优先检查指定的输出路径
            if output_path:
                check_cmd = f"test -f {output_path} && echo 'exists' || echo 'not exists'"
                _, stdout, stderr = self.ssh_client.exec_command(check_cmd)
                check_result = stdout.read().decode().strip()

                if check_result == 'exists':
                    result_img_path = output_path
                    logger.info(f"使用指定的输出路径: {result_img_path}")
                else:
                    logger.warning(f"指定的输出路径不存在: {output_path}")

            # 如果还没有找到结果路径，尝试查找默认的results目录
            if not result_img_path:
                logger.info("尝试查找默认的推理结果目录...")
                find_cmd = f"find {self.remote_dir}/results -name '*.jpg' -o -name '*.png' | head -5"
                _, stdout, stderr = self.ssh_client.exec_command(find_cmd)
                find_output = stdout.read().decode().strip()

                if find_output:
                    # 获取最新的结果文件
                    result_files = find_output.split('\n')
                    if result_files:
                        # 选择最新修改的文件
                        latest_cmd = f"ls -t {' '.join(result_files)} | head -1"
                        _, stdout, stderr = self.ssh_client.exec_command(latest_cmd)
                        latest_file = stdout.read().decode().strip()
                        if latest_file:
                            result_img_path = latest_file
                            logger.info(f"找到最新的推理结果: {result_img_path}")
                        else:
                            logger.warning("未找到有效的推理结果文件")

            # 最后的备用方案：如果仍然没有找到结果图片，但有检测结果，创建一个简单的结果说明
            if not result_img_path and (detections or detection_count > 0):
                logger.info("创建简单的推理结果说明...")
                try:
                    # 创建一个简单的文本结果文件
                    result_text_path = f"{self.remote_dir}/inference_result.txt"
                    result_content = f"推理完成\n检测到 {detection_count} 个目标\n"
                    for i, det in enumerate(detections):
                        result_content += f"目标 {i+1}: {det['class']} (置信度: {det['confidence']:.2f})\n"

                    # 写入结果文件
                    write_cmd = f"echo '{result_content}' > {result_text_path}"
                    self.ssh_client.exec_command(write_cmd)

                    logger.info(f"创建了文本结果文件: {result_text_path}")
                except Exception as e:
                    logger.error(f"创建结果文件失败: {e}")
            
            # 获取图片数据
            image_data = None
            if result_img_path:
                logger.info(f"✅ 推理完成，检测到 {detection_count} 个目标，结果保存在: {result_img_path}")
                
                try:
                    # 创建SFTP客户端
                    sftp = self.ssh_client.open_sftp()
                    # 创建临时文件保存图片
                    import tempfile
                    import base64
                    
                    # 使用临时文件直接读取远程图片
                    new_result_img_path = "/workspace/" + result_img_path
                    with tempfile.NamedTemporaryFile(delete=False) as temp:
                        temp_path = temp.name
                        logger.info(f"下载推理结果图片: {new_result_img_path} -> {temp_path}")
                        sftp.get(new_result_img_path, temp_path)
                    # 读取图片数据并转为base64
                    with open(temp_path, 'rb') as f:
                        image_bytes = f.read()
                        image_data = base64.b64encode(image_bytes).decode('utf-8')
                    #print(image_data)
                    # 删除临时文件
                    os.unlink(temp_path)
                    # 关闭SFTP连接
                    sftp.close()
                    
                    logger.info(f"图片数据获取成功，大小: {len(image_data) / 1024:.2f} KB")
                except Exception as e:
                    logger.error(f"获取图片数据失败: {e}")

                # 清理临时文件
                if temp_file_created and processed_input_source != input_source:
                    try:
                        cleanup_cmd = f"rm -f {processed_input_source}"
                        self.ssh_client.exec_command(cleanup_cmd)
                        logger.info(f"已清理临时文件: {processed_input_source}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {e}")

                return {
                    "success": True,
                    "result_path": result_img_path,
                    "detections": detections,
                    "detection_count": detection_count,
                    "inference_log": output,
                    "image_data": image_data,
                    "image_format": "base64"
                }
            else:
                logger.warning("⚠️ 推理可能成功，但未找到结果图片路径")
                logger.info(f"推理输出详情:\n{output}")

                # 清理临时文件
                if temp_file_created and processed_input_source != input_source:
                    try:
                        cleanup_cmd = f"rm -f {processed_input_source}"
                        self.ssh_client.exec_command(cleanup_cmd)
                        logger.info(f"已清理临时文件: {processed_input_source}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {e}")

                # 即使没有图片，也返回检测结果
                return {
                    "success": True,
                    "detections": detections,
                    "detection_count": detection_count,
                    "inference_log": output,
                    "message": "推理完成，但未找到结果图片。可能是推理脚本输出格式发生变化。"
                }

        except Exception as e:
            logger.error(f"模型推理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 在异常情况下也要清理临时文件
            try:
                if 'temp_file_created' in locals() and temp_file_created and 'processed_input_source' in locals():
                    cleanup_cmd = f"rm -f {processed_input_source}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"异常情况下已清理临时文件: {processed_input_source}")
            except:
                pass  # 忽略清理过程中的错误

            return {"success": False, "error": f"模型推理失败: {str(e)}"}

    def run_batch_inference(self, model_path, input_sources, confidence_threshold=0.5, output_dir=None):
        """
        运行批量模型推理（多张图片）

        Args:
            model_path: 模型文件路径
            input_sources: 输入源列表，每个元素可以是图片路径或base64数据
            confidence_threshold: 置信度阈值
            output_dir: 输出目录 (可选)

        Returns:
            dict: 批量推理结果
        """
        try:
            if not self.ssh_client:
                return {"success": False, "error": "SSH未连接"}

            if not input_sources or len(input_sources) == 0:
                return {"success": False, "error": "输入源列表为空"}

            logger.info(f"🔍 开始批量模型推理: {len(input_sources)} 张图片")

            # 获取远程脚本路径
            remote_script_path = self._get_remote_script_path("model_info_extractor.py")

            # 获取ULTRALYTICS_DIR用于--mount_path参数
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')

            # 获取挂载路径
            mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 设置环境变量命令
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

            # 处理输入源列表 - 上传base64数据到远程服务器
            processed_input_sources = []
            temp_files_created = []

            for i, input_source in enumerate(input_sources):
                processed_input_source = input_source
                temp_file_created = False

                # 检查是否为base64数据
                if len(input_source) > 200:
                    try:
                        import base64
                        import tempfile
                        import time

                        logger.info(f"处理第 {i+1} 张图片的base64数据...")

                        # 生成唯一的临时文件名
                        timestamp = int(time.time())
                        temp_filename = f"temp_batch_input_{timestamp}_{i}.jpg"
                        remote_temp_path = f"{self.remote_dir}/{temp_filename}"

                        # 处理base64数据
                        if input_source.startswith('data:image/'):
                            # 移除data:image/...;base64,前缀
                            header, encoded_data = input_source.split(',', 1)
                        else:
                            encoded_data = input_source

                        # 解码base64数据
                        image_data = base64.b64decode(encoded_data)
                        # 创建本地临时文件
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as local_temp:
                            local_temp.write(image_data)
                            local_temp_path = local_temp.name

                        # 上传到远程服务器
                        sftp = self.ssh_client.open_sftp()
                        sftp.put(local_temp_path, remote_temp_path)
                        sftp.close()

                        # 删除本地临时文件
                        os.unlink(local_temp_path)

                        # 使用远程临时文件路径
                        processed_input_source = remote_temp_path
                        temp_file_created = True

                        logger.info(f"第 {i+1} 张图片已上传到: {remote_temp_path}")

                    except Exception as e:
                        logger.error(f"处理第 {i+1} 张图片的base64数据失败: {e}")
                        # 如果处理失败，仍然尝试直接使用原始数据
                        processed_input_source = input_source

                processed_input_sources.append(processed_input_source)
                temp_files_created.append(temp_file_created)

            # 将输入源列表转换为以逗号分隔的字符串
            input_sources_str = ",".join(processed_input_sources)

            # 构建批量推理命令
            batch_infer_cmd = f"""
cd {self.remote_dir} && \
python {remote_script_path} batch_infer --model {model_path} --images "{input_sources_str}" --conf {confidence_threshold} --ultralytics_mount_path {ultralytics_mount_path} --mount_path {mount_path}
"""

            if output_dir:
                batch_infer_cmd += f" --output_dir {output_dir}"

            # 组合完整命令，添加环境变量设置
            full_cmd = f"bash -c '{set_env_cmd} && {batch_infer_cmd}'"

            logger.info(f"🔍 执行批量推理命令...")

            # 执行批量推理命令
            _, stdout, stderr = self.ssh_client.exec_command(full_cmd)
            output = stdout.read().decode()
            error = stderr.read().decode()

            if error and "错误" in error:
                logger.error(f"❌ 批量模型推理失败: {error}")
                return {
                    "success": False,
                    "error": error,
                    "inference_log": output
                }

            # 解析批量推理结果
            batch_results = self._parse_batch_inference_output(output)

            # 获取结果图片数据
            for result in batch_results:
                if result.get("result_path"):
                    try:
                        # 获取图片的base64数据
                        image_data = self._get_remote_image_data(result["result_path"])
                        result["image_data"] = image_data
                        result["image_format"] = "base64"
                    except Exception as e:
                        logger.error(f"获取结果图片数据失败: {e}")
                        result["image_data"] = None

            # 清理临时文件
            self._cleanup_temp_files(processed_input_sources, temp_files_created)

            logger.info(f"✅ 批量推理完成，处理了 {len(batch_results)} 张图片")

            return {
                "success": True,
                "results": batch_results,
                "total_images": len(input_sources),
                "processed_images": len(batch_results),
                "inference_log": output
            }

        except Exception as e:
            logger.error(f"批量模型推理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 在异常情况下也要清理临时文件
            try:
                if 'temp_files_created' in locals() and 'processed_input_sources' in locals():
                    self._cleanup_temp_files(processed_input_sources, temp_files_created)
            except:
                pass  # 忽略清理过程中的错误

            return {"success": False, "error": f"批量模型推理失败: {str(e)}"}

    def _parse_batch_inference_output(self, output):
        """
        解析批量推理输出结果

        Args:
            output: 推理命令的输出

        Returns:
            list: 解析后的结果列表
        """
        results = []
        current_result = {}

        logger.info(f"解析批量推理输出:\n{output}")

        for line in output.split('\n'):
            line = line.strip()

            if "=== 处理图片" in line:
                # 开始处理新图片
                if current_result:
                    results.append(current_result)
                current_result = {
                    "image_index": len(results),
                    "detections": [],
                    "detection_count": 0,
                    "result_path": None
                }

            elif "推理完成，结果保存在" in line:
                current_result["result_path"] = line.split(": ")[-1].strip()

            elif "BATCH_INFERENCE_RESULT_PATH:" in line:
                # 新的特殊标记格式
                current_result["result_path"] = line.split("BATCH_INFERENCE_RESULT_PATH:")[-1].strip()

            elif "检测到" in line and "个目标" in line:
                try:
                    detection_count = int(line.split(" ")[1])
                    current_result["detection_count"] = detection_count
                except:
                    pass

            elif "目标" in line and "类别=" in line:
                try:
                    parts = line.split(": ")[1].split(", ")
                    cls_name = parts[0].split("=")[1]
                    confidence = float(parts[1].split("=")[1])
                    coords = eval(parts[2].split("=")[1])

                    current_result["detections"].append({
                        "class": cls_name,
                        "confidence": confidence,
                        "bbox": coords
                    })
                except Exception as e:
                    logger.warning(f"解析检测结果行失败: {line}, 错误: {e}")

        # 添加最后一个结果
        if current_result:
            results.append(current_result)

        return results

    def _get_remote_image_data(self, remote_path):
        """
        获取远程图片的base64数据

        Args:
            remote_path: 远程图片路径

        Returns:
            str: base64编码的图片数据
        """
        try:
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()
            # 创建临时文件保存图片
            import tempfile
            import base64

            # 处理路径，确保正确的远程路径
            if not remote_path.startswith("/workspace/"):
                remote_path = "/workspace/" + remote_path.lstrip("/")

            with tempfile.NamedTemporaryFile(delete=False) as temp:
                temp_path = temp.name
                logger.info(f"下载推理结果图片: {remote_path} -> {temp_path}")
                sftp.get(remote_path, temp_path)

            # 读取图片数据并转为base64
            with open(temp_path, 'rb') as f:
                image_bytes = f.read()
                image_data = base64.b64encode(image_bytes).decode('utf-8')

            # 删除临时文件
            os.unlink(temp_path)
            # 关闭SFTP连接
            sftp.close()

            logger.info(f"图片数据获取成功，大小: {len(image_data) / 1024:.2f} KB")
            return image_data

        except Exception as e:
            logger.error(f"获取远程图片数据失败: {e}")
            return None

    def _cleanup_temp_files(self, processed_input_sources, temp_files_created):
        """
        清理临时文件

        Args:
            processed_input_sources: 处理后的输入源列表
            temp_files_created: 临时文件创建标志列表
        """
        for i, (source, is_temp) in enumerate(zip(processed_input_sources, temp_files_created)):
            if is_temp:
                try:
                    cleanup_cmd = f"rm -f {source}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"已清理第 {i+1} 张图片的临时文件: {source}")
                except Exception as e:
                    logger.warning(f"清理第 {i+1} 张图片的临时文件失败: {e}")