#!/usr/bin/env python3
import argparse
import os
import sys
import argparse

import base64
import tempfile
from pathlib import Path
import cv2
import numpy as np
import torch
from tqdm import tqdm
import json
import shutil

# 尝试导入ais_bench，如果失败则提供替代方案
try:
    from ais_bench.infer.interface import InferSession
    AIS_BENCH_AVAILABLE = True
except ImportError:
    print("⚠️  ais_bench未安装，OM推理功能将不可用")
    print("💡 如需使用OM推理，请在华为昇腾环境中安装: pip install ais_bench")
    AIS_BENCH_AVAILABLE = False

    # 提供一个占位符类
    class InferSession:
        def __init__(self, *args, **kwargs):
            raise ImportError("ais_bench not available. Please install ais_bench for OM inference.")

        def infer(self, *args, **kwargs):
            raise ImportError("ais_bench not available. Please install ais_bench for OM inference.")


def load_image_for_om(img_path, img_size=640):
    """
    加载并预处理单张图片用于OM推理
    Args:
        img_path: 图片路径
        img_size: 目标图片尺寸
    Returns:
        tuple: (preprocessed_img, original_img, ratio, pad)
    """
    # 读取图片
    img0 = cv2.imread(img_path)
    if img0 is None:
        raise ValueError(f"无法读取图片: {img_path}")

    # 计算缩放比例
    h0, w0 = img0.shape[:2]  # 原始高度和宽度
    r = img_size / max(h0, w0)  # 缩放比例
    if r != 1:  # 如果需要缩放
        img0 = cv2.resize(img0, (int(w0 * r), int(h0 * r)), interpolation=cv2.INTER_LINEAR)

    # 填充到正方形
    h, w = img0.shape[:2]
    top = (img_size - h) // 2
    bottom = img_size - h - top
    left = (img_size - w) // 2
    right = img_size - w - left

    img = cv2.copyMakeBorder(img0, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))

    # 转换为RGB并归一化
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img = img.transpose(2, 0, 1)  # HWC to CHW
    img = np.ascontiguousarray(img, dtype=np.float32)
    img /= 255.0  # 归一化到0-1

    # 添加batch维度
    img = np.expand_dims(img, axis=0)

    ratio = r
    pad = (left, top)

    return img, img0, ratio, pad


def scale_coords_om(img1_shape, coords, img0_shape, ratio_pad=None):
    """
    将坐标从img1_shape缩放到img0_shape
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0]
        pad = ratio_pad[1]

    coords[:, [0, 2]] -= pad[0]  # x padding
    coords[:, [1, 3]] -= pad[1]  # y padding
    coords[:, :4] /= gain
    coords[:, 0] = torch.clamp(coords[:, 0], 0, img0_shape[1])  # x1
    coords[:, 1] = torch.clamp(coords[:, 1], 0, img0_shape[0])  # y1
    coords[:, 2] = torch.clamp(coords[:, 2], 0, img0_shape[1])  # x2
    coords[:, 3] = torch.clamp(coords[:, 3], 0, img0_shape[0])  # y2
    return coords


def non_max_suppression_om(prediction, conf_thres=0.25, iou_thres=0.45, max_det=300):
    """
    OM推理的非极大值抑制
    """
    # 确保输入是torch tensor
    if isinstance(prediction, np.ndarray):
        prediction = torch.from_numpy(prediction)

    print(f"原始预测形状: {prediction.shape}")

    # 处理不同的输出格式
    if prediction.dim() == 3:
        # 如果是 [1, 84, 8400] 格式，需要转置为 [1, 8400, 84]
        if prediction.shape[1] == 84 and prediction.shape[2] == 8400:
            prediction = prediction.transpose(1, 2)  # [1, 8400, 84]
            print(f"转置后形状: {prediction.shape}")

        # 去掉batch维度
        prediction = prediction.squeeze(0)  # [8400, 84]
    elif prediction.dim() == 4:
        prediction = prediction.squeeze(0)

    print(f"处理后形状: {prediction.shape}")

    # 确保形状正确 [N, 84] 其中N是检测框数量
    if prediction.shape[1] != 84:
        raise ValueError(f"预期输出形状为 [N, 84]，但得到 {prediction.shape}")

    # 置信度过滤 - objectness score在第4列
    conf_mask = prediction[:, 4] > conf_thres
    prediction = prediction[conf_mask]

    if not prediction.size(0):
        return [torch.empty((0, 6))]

    # 计算类别置信度 - 类别分数从第5列开始
    prediction[:, 5:] *= prediction[:, 4:5]  # conf = obj_conf * cls_conf

    # Box (center x, center y, width, height) to (x1, y1, x2, y2)
    box = prediction[:, :4].clone()
    box[:, 0] = prediction[:, 0] - prediction[:, 2] / 2  # x1
    box[:, 1] = prediction[:, 1] - prediction[:, 3] / 2  # y1
    box[:, 2] = prediction[:, 0] + prediction[:, 2] / 2  # x2
    box[:, 3] = prediction[:, 1] + prediction[:, 3] / 2  # y2
    prediction[:, :4] = box

    # 获取最大类别置信度和索引
    conf, j = prediction[:, 5:].max(1, keepdim=True)
    prediction = torch.cat((prediction[:, :4], conf, j.float()), 1)[conf.view(-1) > conf_thres]

    if not prediction.size(0):
        return [torch.empty((0, 6))]

    # 按置信度排序
    prediction = prediction[prediction[:, 4].argsort(descending=True)]

    # NMS - 使用torchvision的nms函数
    try:
        import torchvision
        keep = torchvision.ops.nms(prediction[:, :4], prediction[:, 4], iou_thres)
    except ImportError:
        # 如果没有torchvision，使用简单的NMS实现
        keep = simple_nms(prediction[:, :4], prediction[:, 4], iou_thres)

    if keep.shape[0] > max_det:
        keep = keep[:max_det]

    return [prediction[keep]]


def simple_nms(boxes, scores, iou_threshold):
    """
    简单的NMS实现，当torchvision不可用时使用
    """
    if boxes.numel() == 0:
        return torch.empty((0,), dtype=torch.int64)

    # 按分数排序
    _, order = scores.sort(descending=True)

    keep = []
    while order.numel() > 0:
        if order.numel() == 1:
            keep.append(order[0])
            break

        i = order[0]
        keep.append(i)

        # 计算IoU
        iou = box_iou(boxes[i:i+1], boxes[order[1:]])

        # 保留IoU小于阈值的框
        mask = iou <= iou_threshold
        order = order[1:][mask.squeeze()]

    return torch.tensor(keep, dtype=torch.int64)


def box_iou(box1, box2):
    """
    计算两个框的IoU
    """
    # 计算交集
    inter_x1 = torch.max(box1[:, 0:1], box2[:, 0])
    inter_y1 = torch.max(box1[:, 1:2], box2[:, 1])
    inter_x2 = torch.min(box1[:, 2:3], box2[:, 2])
    inter_y2 = torch.min(box1[:, 3:4], box2[:, 3])

    inter_area = torch.clamp(inter_x2 - inter_x1, min=0) * torch.clamp(inter_y2 - inter_y1, min=0)

    # 计算并集
    box1_area = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
    box2_area = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
    union_area = box1_area.unsqueeze(1) + box2_area - inter_area

    return inter_area / union_area








def process_input_source(input_source):
    """
    处理输入源，支持文件路径和base64数据

    Args:
        input_source: 输入源，可以是文件路径或base64编码的图片数据

    Returns:
        tuple: (processed_path, is_temp_file) 处理后的文件路径和是否为临时文件的标志
    """
    # 检查是否为base64数据
    if input_source.startswith('data:image/') or (len(input_source) > 100 and not os.path.exists(input_source)):
        try:
            # 处理base64数据
            if input_source.startswith('data:image/'):
                # 移除data:image/...;base64,前缀
                header, encoded_data = input_source.split(',', 1)
                # 从header中提取文件格式
                format_info = header.split(';')[0].split('/')[-1]
                if format_info.lower() in ['jpeg', 'jpg']:
                    ext = '.jpg'
                elif format_info.lower() == 'png':
                    ext = '.png'
                else:
                    ext = '.jpg'  # 默认
            else:
                # 纯base64数据，默认为jpg
                encoded_data = input_source
                ext = '.jpg'

            # 解码base64数据
            image_data = base64.b64decode(encoded_data)

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=ext)
            temp_file.write(image_data)
            temp_file.close()

            print(f"Base64数据已转换为临时文件: {temp_file.name}")
            return temp_file.name, True

        except Exception as e:
            print(f"处理base64数据失败: {e}")
            raise ValueError(f"无效的base64图片数据: {e}")
    else:
        # 普通文件路径
        if not os.path.exists(input_source):
            raise FileNotFoundError(f"图片文件不存在: {input_source}")
        return input_source, False


# 置信度阈值
CONFIDENCE = 0.4
# NMS 的 IoU 阈值
IOU = 0.45

def get_classes_from_pt_model(pt_model_path):
    """
    从.pt模型文件中获取类别信息

    Args:
        pt_model_path: PT模型文件路径

    Returns:
        dict: 类别ID到类别名称的映射字典
    """
    try:
        from ultralytics import YOLO
        model = YOLO(pt_model_path)
        return model.names
    except Exception as e:
        print(f"从PT模型获取类别信息失败: {e}")
        # 返回默认的COCO类别
        return {
            0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck',
            8: 'boat', 9: 'traffic light', 10: 'fire hydrant', 11: 'stop sign', 12: 'parking meter', 13: 'bench',
            14: 'bird', 15: 'cat', 16: 'dog', 17: 'horse', 18: 'sheep', 19: 'cow', 20: 'elephant', 21: 'bear',
            22: 'zebra', 23: 'giraffe', 24: 'backpack', 25: 'umbrella', 26: 'handbag', 27: 'tie', 28: 'suitcase',
            29: 'frisbee', 30: 'skis', 31: 'snowboard', 32: 'sports ball', 33: 'kite', 34: 'baseball bat',
            35: 'baseball glove', 36: 'skateboard', 37: 'surfboard', 38: 'tennis racket', 39: 'bottle',
            40: 'wine glass', 41: 'cup', 42: 'fork', 43: 'knife', 44: 'spoon', 45: 'bowl', 46: 'banana', 47: 'apple',
            48: 'sandwich', 49: 'orange', 50: 'broccoli', 51: 'carrot', 52: 'hot dog', 53: 'pizza', 54: 'donut',
            55: 'cake', 56: 'chair', 57: 'couch', 58: 'potted plant', 59: 'bed', 60: 'dining table', 61: 'toilet',
            62: 'tv', 63: 'laptop', 64: 'mouse', 65: 'remote', 66: 'keyboard', 67: 'cell phone', 68: 'microwave',
            69: 'oven', 70: 'toaster', 71: 'sink', 72: 'refrigerator', 73: 'book', 74: 'clock', 75: 'vase',
            76: 'scissors', 77: 'teddy bear', 78: 'hair drier', 79: 'toothbrush'
        }

def draw_bounding_box(img, class_id, confidence, x, y, x_plus_w, y_plus_h, classes, colors):
    """
    在图像上绘制边界框和类别标签（参考test文件的实现）

    参数：
        img - 原始图像
        class_id - 类别ID
        confidence - 置信度
        x, y - 左上角坐标
        x_plus_w, y_plus_h - 右下角坐标
        classes - 类别字典
        colors - 颜色数组
    """
    label = "{} {:.2f}".format(classes[class_id], confidence)
    color = colors[class_id] if class_id < len(colors) else (0, 255, 0)

    # 画框
    cv2.rectangle(img, (x, y), (x_plus_w, y_plus_h), color, 2)

    # 获取文本大小
    label_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
    label_width, label_height = label_size

    label_x = x
    label_y = y - 10 if y - 10 > label_height else y + 10

    # 背景框
    cv2.rectangle(img, (label_x, label_y - label_height),
                  (label_x + label_width, label_y + label_height), color, cv2.FILLED)
    # 文字
    cv2.putText(img, label, (label_x, label_y), cv2.FONT_HERSHEY_SIMPLEX,
                0.5, (0, 0, 0), 1, cv2.LINE_AA)

def infer_single_image_om(session, original_image, classes, colors):
    """
    使用OM模型对单张图片进行推理（参考test文件的main函数逻辑）

    参数：
        session - OM模型会话
        original_image - 原始图像
        classes - 类别字典
        colors - 颜色数组

    返回：
        original_image - 画框的图片
        detections - 包含每个目标信息的列表
    """
    height, width, _ = original_image.shape

    # 变为正方形图像用于推理
    length = max(height, width)
    image = np.zeros((length, length, 3), np.uint8)
    image[0:height, 0:width] = original_image

    # 缩放因子
    scale = length / 640

    # 预处理图像
    blob = cv2.dnn.blobFromImage(image, scalefactor=1.0 / 255, size=(640, 640), swapRB=True)

    # 模型推理
    outputs = session.infer(feeds=[blob], mode="static")

    # 转换输出维度：从 (1, 84, 8400) -> (8400, 84)
    outputs = np.array([cv2.transpose(outputs[0][0])])
    rows = outputs.shape[1]

    boxes = []
    scores = []
    class_ids = []

    # 解析输出
    for i in range(rows):
        classes_scores = outputs[0][i][4:]
        (_, maxScore, _, maxClassIndex) = cv2.minMaxLoc(classes_scores)
        if maxScore >= CONFIDENCE:
            box = [
                (outputs[0][i][0] - outputs[0][i][2] / 2) * scale,  # x 左上角
                (outputs[0][i][1] - outputs[0][i][3] / 2) * scale,  # y 左上角
                outputs[0][i][2] * scale,  # 宽
                outputs[0][i][3] * scale   # 高
            ]
            boxes.append(box)
            scores.append(maxScore)
            class_ids.append(maxClassIndex)

    # 非极大值抑制
    result_boxes = cv2.dnn.NMSBoxes(boxes, scores, CONFIDENCE, IOU, 0.5)

    detections = []

    # 绘制边界框
    for i in range(len(result_boxes)):
        index = result_boxes[i]
        box = boxes[index]
        detection = {
            "class_id": class_ids[index],
            "class_name": classes[class_ids[index]],
            "confidence": scores[index],
            "box": box,
            "scale": scale,
        }
        detections.append(detection)
        draw_bounding_box(
            original_image,
            class_ids[index],
            scores[index],
            round(box[0]),
            round(box[1]),
            round(box[0] + box[2]),
            round(box[1] + box[3]),
            classes,
            colors
        )

    return original_image, detections

def infer_model_om(om_model_path, pt_model_path, image_paths, save_path="/workspace/", conf_thres=0.25, device_id=0):
    """
    使用OM模型进行图片推理（支持单张或多张图片）

    Args:
        om_model_path: OM模型文件路径
        pt_model_path: PT模型文件路径（用于获取类别名称）
        image_paths: 输入图片路径或路径列表，支持base64数据
        save_path: 结果保存路径，默认为workspace
        conf_thres: 置信度阈值
        device_id: NPU设备ID

    Returns:
        list: 结果图片保存路径列表
    """
    # 检查ais_bench可用性
    if not AIS_BENCH_AVAILABLE:
        print("❌ ais_bench不可用，无法进行OM推理")
        print("💡 请在华为昇腾环境中安装: pip install ais_bench")
        print("🔄 回退到使用PyTorch模型进行推理")
        return infer_model(pt_model_path, image_paths, save_path, conf_thres)

    # 从PT模型获取类别信息
    classes = get_classes_from_pt_model(pt_model_path)
    print(f"从模型获取到 {len(classes)} 个类别")

    # 为每个类别分配随机颜色
    colors = np.random.uniform(0, 255, size=(len(classes), 3))

    # 初始化OM模型
    try:
        om_model = InferSession(int(device_id), om_model_path)
        print(f"✅ 成功加载OM模型: {om_model_path}")
    except Exception as e:
        print(f"❌ OM模型加载失败: {e}")
        print("💡 请检查模型路径和NPU环境")
        print("🔄 回退到使用PyTorch模型进行推理")
        return infer_model(pt_model_path, image_paths, save_path, conf_thres)

    # 处理输入路径，支持单张图片或多张图片
    if isinstance(image_paths, str):
        # 单张图片
        image_paths = [image_paths]

    result_paths = []
    temp_files_to_cleanup = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 处理输入源
            processed_image_path, is_temp_file = process_input_source(image_path)

            if is_temp_file:
                temp_files_to_cleanup.append(processed_image_path)

            print(f"使用OM模型对图片进行推理: {processed_image_path}")

            # 读取图片
            original_image = cv2.imread(processed_image_path)
            if original_image is None:
                print(f"❌ 无法读取图片: {processed_image_path}")
                result_paths.append(None)
                continue

            # 使用test文件的推理逻辑
            result_image, detections = infer_single_image_om(om_model, original_image, classes, colors)

            # 保存结果图片
            if save_path:
                if len(image_paths) == 1:
                    # 单张图片，直接使用指定路径
                    result_img_path = save_path
                else:
                    # 多张图片，为每张图片创建独立的文件名
                    base_name = os.path.splitext(os.path.basename(save_path))[0]
                    ext = os.path.splitext(save_path)[1] or '.jpg'
                    result_img_path = os.path.join(os.path.dirname(save_path), f"{base_name}_{i+1}{ext}")

                os.makedirs(os.path.dirname(result_img_path), exist_ok=True)
                cv2.imwrite(result_img_path, result_image)
            else:
                # 保存到默认位置
                result_dir = "/workspace/om_results"
                os.makedirs(result_dir, exist_ok=True)
                result_filename = f"om_result_{i+1}_{os.path.basename(processed_image_path)}"
                result_img_path = os.path.join(result_dir, result_filename)
                cv2.imwrite(result_img_path, result_image)

            result_paths.append(result_img_path)
            print(f"OM推理完成，结果保存在: {result_img_path}")
            print(f"检测到 {len(detections)} 个目标")

            # 打印检测结果
            for j, detection in enumerate(detections):
                print(f"目标 {j+1}: 类别={detection['class_name']}, 置信度={detection['confidence']:.2f}, 坐标={detection['box']}")

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            result_paths.append(None)

    # 清理临时文件
    for temp_file in temp_files_to_cleanup:
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    print(f"\n推理完成，成功处理 {len([p for p in result_paths if p])} / {len(image_paths)} 张图片")

    # 如果只有一张图片，返回单个路径而不是列表
    if len(image_paths) == 1:
        return result_paths[0] if result_paths else None

    return result_paths


def infer_model(model_path, image_paths, save_path="/workspace/", conf_thres=0.25):
    """
    使用YOLO模型进行图片推理（支持单张或多张图片）

    Args:
        model_path: 模型文件路径
        image_paths: 输入图片路径或路径列表，支持base64数据
        save_path: 结果保存路径，默认为workspace
        conf_thres: 置信度阈值

    Returns:
        str or list: 结果图片保存路径（单张图片返回字符串，多张图片返回列表）
    """
    from ultralytics import YOLO

    # 暂时写死，使用最佳模型
    model_path = os.path.join('/root/siton-data-b496463103254f46976c4ff88ea74bc9', 'data', 'best.pt')
    print(f"加载模型: '{model_path}'")
    model = YOLO(model_path)

    # 处理输入路径，支持单张图片或多张图片
    if isinstance(image_paths, str):
        # 单张图片
        image_paths = [image_paths]

    result_paths = []
    temp_files_to_cleanup = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 处理输入源
            processed_image_path, is_temp_file = process_input_source(image_path)

            if is_temp_file:
                temp_files_to_cleanup.append(processed_image_path)

            print(f"对图片进行推理: {processed_image_path}")
            results = model.predict(
                source=processed_image_path,
                conf=conf_thres,
                save=True,
                save_txt=True,
                save_conf=True,
                project="results",
                imgsz=1024,
                name=os.path.splitext(os.path.basename(processed_image_path))[0]
            )

            # 获取结果图片路径
            result_path = results[0].save_dir
            result_img = os.path.join(result_path, os.path.basename(processed_image_path))

            # 如果指定了保存路径，则复制结果
            if save_path:
                if len(image_paths) == 1:
                    # 单张图片，直接使用指定路径
                    final_result_path = save_path
                else:
                    # 多张图片，为每张图片创建独立的文件名
                    base_name = os.path.splitext(os.path.basename(save_path))[0]
                    ext = os.path.splitext(save_path)[1] or '.jpg'
                    final_result_path = os.path.join(os.path.dirname(save_path), f"{base_name}_{i+1}{ext}")

                import shutil
                os.makedirs(os.path.dirname(final_result_path), exist_ok=True)
                shutil.copy(result_img, final_result_path)
                result_img = final_result_path

            result_paths.append(result_img)
            print(f"推理完成，结果保存在: {result_img}")

            # 打印检测结果
            for r in results:
                boxes = r.boxes
                print(f"检测到 {len(boxes)} 个目标")
                for j, box in enumerate(boxes):
                    cls = int(box.cls[0])
                    conf = float(box.conf[0])
                    name = model.names[cls]
                    print(f"目标 {j+1}: 类别={name}, 置信度={conf:.2f}, 坐标={box.xyxy[0].tolist()}")

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            result_paths.append(None)

    # 清理临时文件
    for temp_file in temp_files_to_cleanup:
        try:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
                print(f"已清理临时文件: {temp_file}")
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    print(f"\n推理完成，成功处理 {len([p for p in result_paths if p])} / {len(image_paths)} 张图片")

    # 如果只有一张图片，返回单个路径而不是列表
    if len(image_paths) == 1:
        return result_paths[0] if result_paths else None

    return result_paths


def batch_infer_model_om(om_model_path, pt_model_path, image_paths_str, conf_thres=0.25, output_dir="/workspace/batch_results/", device_id=0):
    """
    使用OM模型进行批量推理

    Args:
        om_model_path: OM模型文件路径
        pt_model_path: PT模型文件路径（用于获取类别名称）
        image_paths_str: 输入图片路径列表（以逗号分隔的字符串）
        conf_thres: 置信度阈值
        output_dir: 结果保存目录，默认为workspace/batch_results/
        device_id: NPU设备ID

    Returns:
        list: 结果图片保存路径列表
    """
    # 解析图片路径列表
    image_paths = [path.strip() for path in image_paths_str.split(',') if path.strip()]

    if not image_paths:
        print("错误: 没有提供有效的图片路径")
        return []

    print(f"开始使用OM模型批量推理 {len(image_paths)} 张图片")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 使用更新后的infer_model_om函数进行批量推理
    results_paths = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 使用OM模型处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 为每张图片创建独立的输出目录
            image_output_dir = os.path.join(output_dir, f"image_{i+1}")
            os.makedirs(image_output_dir, exist_ok=True)

            # 设置保存路径
            save_path = os.path.join(image_output_dir, f"om_result_{i+1}.jpg")

            # 调用单张图片推理函数
            result_path = infer_model_om(om_model_path, pt_model_path, image_path, save_path, conf_thres, device_id)

            if result_path:
                print(f"BATCH_INFERENCE_RESULT_PATH:{result_path}")
                results_paths.append(result_path)
            else:
                results_paths.append(None)

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            results_paths.append(None)

    print(f"\nOM批量推理完成，成功处理 {len([p for p in results_paths if p])} / {len(image_paths)} 张图片")
    return results_paths


def batch_infer_model(model_path, image_paths_str, conf_thres=0.25, output_dir="/workspace/batch_results/"):
    """
    使用YOLO模型进行批量推理

    Args:
        model_path: 模型文件路径
        image_paths_str: 输入图片路径列表（以逗号分隔的字符串）
        output_dir: 结果保存目录，默认为workspace/batch_results/
        conf_thres: 置信度阈值

    Returns:
        list: 结果图片保存路径列表
    """
    # 解析图片路径列表
    image_paths = [path.strip() for path in image_paths_str.split(',') if path.strip()]

    if not image_paths:
        print("错误: 没有提供有效的图片路径")
        return []

    print(f"开始批量推理 {len(image_paths)} 张图片")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 使用更新后的infer_model函数进行批量推理
    results_paths = []

    for i, image_path in enumerate(image_paths):
        print(f"\n=== 处理图片 {i+1}/{len(image_paths)}: {image_path[:50]}{'...' if len(image_path) > 50 else ''} ===")

        try:
            # 为每张图片创建独立的输出目录
            image_output_dir = os.path.join(output_dir, f"image_{i+1}")
            os.makedirs(image_output_dir, exist_ok=True)

            # 设置保存路径
            save_path = os.path.join(image_output_dir, f"result_{i+1}.jpg")

            # 调用单张图片推理函数
            result_path = infer_model(model_path, image_path, save_path, conf_thres)

            if result_path:
                print(f"BATCH_INFERENCE_RESULT_PATH:{result_path}")
                results_paths.append(result_path)
            else:
                results_paths.append(None)

        except Exception as e:
            print(f"处理图片 {i+1} 时发生错误: {e}")
            results_paths.append(None)

    print(f"\n批量推理完成，成功处理 {len([p for p in results_paths if p])} / {len(image_paths)} 张图片")
    return results_paths


def main():
    parser = argparse.ArgumentParser(description="YOLOv8模型推理工具")
    subparsers = parser.add_subparsers(dest="command", help="选择命令")
    
    # 推理命令
    infer_parser = subparsers.add_parser("infer", help="使用模型进行单张图片推理")
    infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    infer_parser.add_argument('--image', required=True, help='输入图片路径')
    infer_parser.add_argument('--save_path', help='结果保存路径')
    # 添加数据和代码路径参数
    infer_parser.add_argument('--mount_path', help='数据挂载路径')
    infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # OM推理命令
    om_infer_parser = subparsers.add_parser("om_infer", help="使用OM模型进行单张图片推理")
    om_infer_parser.add_argument('--om_model', required=True, help='OM模型路径(.om文件)')
    om_infer_parser.add_argument('--pt_model', required=True, help='PT模型路径(.pt文件，用于获取类别名称)')
    om_infer_parser.add_argument('--image', required=True, help='输入图片路径')
    om_infer_parser.add_argument('--save_path', help='结果保存路径')
    om_infer_parser.add_argument('--device_id', type=int, default=0, help='NPU设备ID')
    om_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    om_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    om_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # 批量推理命令
    batch_infer_parser = subparsers.add_parser("batch_infer", help="使用模型进行批量推理")
    batch_infer_parser.add_argument('--model', required=True, help='模型路径(.pt, .onnx或其他支持的格式)')
    batch_infer_parser.add_argument('--images', required=True, help='输入图片路径列表（以逗号分隔）')
    batch_infer_parser.add_argument('--output_dir', help='结果保存目录')
    # 添加数据和代码路径参数
    batch_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    batch_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    batch_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    # OM批量推理命令
    om_batch_infer_parser = subparsers.add_parser("om_batch_infer", help="使用OM模型进行批量推理")
    om_batch_infer_parser.add_argument('--om_model', required=True, help='OM模型路径(.om文件)')
    om_batch_infer_parser.add_argument('--pt_model', required=True, help='PT模型路径(.pt文件，用于获取类别名称)')
    om_batch_infer_parser.add_argument('--images', required=True, help='输入图片路径列表（以逗号分隔）')
    om_batch_infer_parser.add_argument('--output_dir', help='结果保存目录')
    om_batch_infer_parser.add_argument('--device_id', type=int, default=0, help='NPU设备ID')
    om_batch_infer_parser.add_argument('--mount_path', help='数据挂载路径')
    om_batch_infer_parser.add_argument('--ultralytics_mount_path', help='ultralytics源码路径')
    om_batch_infer_parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')

    args = parser.parse_args()


    # 如果指定了ultralytics_dir，则更新Python路径
    if args.ultralytics_mount_path:
        sys.path.insert(0, str(args.ultralytics_mount_path))
        os.environ["PYTHONPATH"] = str(args.ultralytics_mount_path)
    
    # 如果指定了mount_path，则更新模型路径
    # if args.mount_path:
    #     args.model = os.path.join(args.mount_path, 'data', os.path.basename(args.model))
    
    if args.command == "infer":
        infer_model(args.model, args.image, args.save_path, args.conf)
    elif args.command == "om_infer":
        infer_model_om(args.om_model, args.pt_model, args.image, args.save_path, args.conf, args.device_id)
    elif args.command == "batch_infer":
        if args.output_dir:
            batch_infer_model(args.model, args.images, args.conf, args.output_dir)
        else:
            batch_infer_model(args.model, args.images, args.conf)
    elif args.command == "om_batch_infer":
        if args.output_dir:
            batch_infer_model_om(args.om_model, args.pt_model, args.images, args.conf, args.output_dir, args.device_id)
        else:
            batch_infer_model_om(args.om_model, args.pt_model, args.images, args.conf, device_id=args.device_id)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()